// netlify/functions/syntactic-restructuring.js
// Advanced syntactic tree restructuring for aggressive text transformation
// Achieves ≤10% AI detection through sentence structure manipulation

/**
 * Syntactic patterns for sentence restructuring
 */
const RESTRUCTURING_PATTERNS = {
    // Subject-Verb-Object transformations
    svo: {
        // "The system processes data" → "Data gets processed by the system"
        activeToPassive: /^(.+?)\s+(processes|handles|manages|creates|generates|produces|analyzes|evaluates|implements|executes|performs|conducts)\s+(.+)$/i,
        // "Users can access the feature" → "The feature is accessible to users"
        modalToAdjective: /^(.+?)\s+can\s+(access|use|modify|control|manage)\s+(.+)$/i,
        // "This approach provides benefits" → "Benefits are provided through this approach"
        verbToNoun: /^(.+?)\s+(provides|offers|delivers|ensures|guarantees|enables|facilitates)\s+(.+)$/i
    },
    
    // Complex sentence splitting and merging
    complexity: {
        // Split compound sentences
        compound: /^(.+?),\s+(and|but|or|yet|so)\s+(.+)$/,
        // Merge simple sentences with connectors
        simple: /^(.+?)\.\s*(.+?)\.$/,
        // Transform conditional structures
        conditional: /^If\s+(.+?),\s+then\s+(.+)$/i
    },
    
    // Clause reordering patterns
    clauses: {
        // Move dependent clauses
        dependent: /^(.+?)\s+(because|since|although|while|whereas|unless|until)\s+(.+)$/i,
        // Reorder relative clauses
        relative: /^(.+?)\s+(which|that|who|whom|whose)\s+(.+)$/i,
        // Transform participial phrases
        participial: /^(.+?),\s+(having|being|considering|regarding)\s+(.+)$/i
    }
};

/**
 * Transformation templates for different sentence structures
 */
const TRANSFORMATION_TEMPLATES = {
    activeToPassive: [
        "{object} gets {verb}ed by {subject}",
        "{object} is {verb}ed through {subject}",
        "{object} undergoes {verb}ing via {subject}"
    ],
    
    modalToAdjective: [
        "{object} is {verb}ible to {subject}",
        "{object} remains {verb}ible for {subject}",
        "{subject} find {object} {verb}ible"
    ],
    
    verbToNoun: [
        "{object} are {verb}d through {subject}",
        "{subject} results in {object}",
        "{object} emerge from {subject}"
    ],
    
    conditionalReverse: [
        "{consequence} occurs when {condition}",
        "{condition} leads to {consequence}",
        "{consequence} happens provided {condition}"
    ],
    
    clauseReorder: [
        "{dependent} {main}",
        "{main}, given that {dependent}",
        "Considering {dependent}, {main}"
    ]
};

/**
 * Verb conjugation helpers for transformations
 */
const VERB_CONJUGATIONS = {
    process: { past: 'processed', ing: 'processing', able: 'processable' },
    handle: { past: 'handled', ing: 'handling', able: 'handleable' },
    manage: { past: 'managed', ing: 'managing', able: 'manageable' },
    create: { past: 'created', ing: 'creating', able: 'creatable' },
    generate: { past: 'generated', ing: 'generating', able: 'generatable' },
    analyze: { past: 'analyzed', ing: 'analyzing', able: 'analyzable' },
    evaluate: { past: 'evaluated', ing: 'evaluating', able: 'evaluatable' },
    implement: { past: 'implemented', ing: 'implementing', able: 'implementable' },
    execute: { past: 'executed', ing: 'executing', able: 'executable' },
    access: { past: 'accessed', ing: 'accessing', able: 'accessible' },
    use: { past: 'used', ing: 'using', able: 'usable' },
    modify: { past: 'modified', ing: 'modifying', able: 'modifiable' },
    control: { past: 'controlled', ing: 'controlling', able: 'controllable' },
    provide: { past: 'provided', ing: 'providing', noun: 'provision' },
    offer: { past: 'offered', ing: 'offering', noun: 'offering' },
    deliver: { past: 'delivered', ing: 'delivering', noun: 'delivery' },
    ensure: { past: 'ensured', ing: 'ensuring', noun: 'assurance' },
    enable: { past: 'enabled', ing: 'enabling', noun: 'enablement' },
    facilitate: { past: 'facilitated', ing: 'facilitating', noun: 'facilitation' }
};

/**
 * Apply syntactic restructuring to a sentence
 */
function restructureSentence(sentence, aggressiveness = 0.7, userTier = 'free') {
    if (!sentence || sentence.length < 10) return sentence;
    
    let result = sentence.trim();
    const transformationProbability = aggressiveness * (userTier === 'admin' ? 1.0 : userTier === 'premium' ? 0.8 : 0.6);
    
    // Apply transformations based on probability
    if (Math.random() < transformationProbability) {
        // Try active to passive transformation
        const activeMatch = result.match(RESTRUCTURING_PATTERNS.svo.activeToPassive);
        if (activeMatch) {
            const [, subject, verb, object] = activeMatch;
            const verbInfo = VERB_CONJUGATIONS[verb.toLowerCase()];
            if (verbInfo) {
                const templates = TRANSFORMATION_TEMPLATES.activeToPassive;
                const template = templates[Math.floor(Math.random() * templates.length)];
                result = template
                    .replace('{object}', object.trim())
                    .replace('{verb}', verb.toLowerCase())
                    .replace('{subject}', subject.trim());
            }
        }
    }
    
    if (Math.random() < transformationProbability * 0.8) {
        // Try modal to adjective transformation
        const modalMatch = result.match(RESTRUCTURING_PATTERNS.svo.modalToAdjective);
        if (modalMatch) {
            const [, subject, verb, object] = modalMatch;
            const verbInfo = VERB_CONJUGATIONS[verb.toLowerCase()];
            if (verbInfo && verbInfo.able) {
                const templates = TRANSFORMATION_TEMPLATES.modalToAdjective;
                const template = templates[Math.floor(Math.random() * templates.length)];
                result = template
                    .replace('{object}', object.trim())
                    .replace('{verb}', verb.toLowerCase())
                    .replace('{subject}', subject.trim());
            }
        }
    }
    
    if (Math.random() < transformationProbability * 0.7) {
        // Try clause reordering
        const dependentMatch = result.match(RESTRUCTURING_PATTERNS.clauses.dependent);
        if (dependentMatch) {
            const [, main, connector, dependent] = dependentMatch;
            if (Math.random() < 0.5) {
                result = `${connector.charAt(0).toUpperCase() + connector.slice(1)} ${dependent.trim()}, ${main.trim().toLowerCase()}`;
            }
        }
    }
    
    return result;
}

/**
 * Apply complex sentence transformations
 */
function applyComplexTransformations(text, options = {}) {
    const { aggressiveness = 0.7, userTier = 'free' } = options;
    
    // Split into sentences
    const sentences = text.match(/[^.!?]*[.!?]+/g) || [text];
    const transformedSentences = [];
    
    for (let i = 0; i < sentences.length; i++) {
        let sentence = sentences[i].trim();
        
        if (sentence.length < 10) {
            transformedSentences.push(sentence);
            continue;
        }
        
        // Apply sentence-level restructuring
        sentence = restructureSentence(sentence, aggressiveness, userTier);
        
        // Apply compound sentence splitting (premium/admin only)
        if (userTier !== 'free' && Math.random() < aggressiveness * 0.4) {
            const compoundMatch = sentence.match(RESTRUCTURING_PATTERNS.complexity.compound);
            if (compoundMatch) {
                const [, part1, connector, part2] = compoundMatch;
                // Split into two sentences with transitional phrases
                const transitions = {
                    'and': ['Additionally', 'Furthermore', 'Moreover'],
                    'but': ['However', 'Nevertheless', 'Conversely'],
                    'or': ['Alternatively', 'Otherwise', 'Instead'],
                    'so': ['Therefore', 'Consequently', 'As a result'],
                    'yet': ['Nevertheless', 'However', 'Still']
                };
                
                const transitionWords = transitions[connector.toLowerCase()] || ['Additionally'];
                const transition = transitionWords[Math.floor(Math.random() * transitionWords.length)];
                
                sentence = `${part1.trim()}. ${transition}, ${part2.trim()}`;
            }
        }
        
        transformedSentences.push(sentence);
    }
    
    return transformedSentences.join(' ').replace(/\s+/g, ' ').trim();
}

/**
 * Performance-optimized processing with caching
 */
function processWithCaching(text, options = {}) {
    const { userTier = 'free' } = options;
    
    // Simple caching for premium/admin users
    if (userTier !== 'free') {
        const cacheKey = `syntactic_${text.substring(0, 50)}_${JSON.stringify(options)}`;
        // In a real implementation, you'd use a proper cache here
    }
    
    return applyComplexTransformations(text, options);
}

/**
 * Main export function for Netlify
 */
exports.handler = async (event, context) => {
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
    
    try {
        const { text, options = {} } = JSON.parse(event.body);
        
        if (!text || typeof text !== 'string') {
            return {
                statusCode: 400,
                body: JSON.stringify({ error: 'Text is required and must be a string' })
            };
        }
        
        const startTime = Date.now();
        const result = processWithCaching(text, options);
        const processingTime = Date.now() - startTime;
        
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                success: true,
                text: result,
                originalText: text,
                method: 'syntactic-restructuring',
                processingTime,
                transformationRate: ((text.length - result.length) / text.length * 100).toFixed(1),
                algorithm: 'syntactic-tree-restructuring'
            })
        };
        
    } catch (error) {
        return {
            statusCode: 500,
            body: JSON.stringify({
                success: false,
                error: error.message,
                method: 'syntactic-restructuring'
            })
        };
    }
};

// Export for local testing
module.exports = {
    restructureSentence,
    applyComplexTransformations,
    processWithCaching
};
