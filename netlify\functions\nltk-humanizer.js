// netlify/functions/nltk-humanizer.js
// Self-contained NLTK-inspired humanization for Netlify
// Enhanced to match falconService.js quality
// No external dependencies, pure JavaScript implementation

/**
 * Enhanced synonym replacement system with contextual understanding
 * Redesigned for maximum effectiveness and natural language preservation
 */
function getAdvancedSynonyms(word, posTag, context = '') {
    // Enhanced synonym database with contextual awareness and naturalness scoring
    const synonymDatabase = {
        // Adjectives (JJ, JJR, JJS) - ordered by naturalness (most natural first)
        'important': {
            casual: ['key', 'big', 'major', 'main', 'crucial'],
            formal: ['significant', 'vital', 'essential', 'critical'],
            context_aware: {
                'business': ['key', 'critical', 'major'],
                'academic': ['significant', 'crucial', 'vital'],
                'casual': ['big', 'major', 'key']
            }
        },
        'significant': {
            casual: ['big', 'major', 'key', 'important'],
            formal: ['substantial', 'considerable', 'notable', 'meaningful'],
            context_aware: {
                'research': ['notable', 'substantial', 'meaningful'],
                'business': ['major', 'key', 'important']
            }
        },
        'excellent': {
            casual: ['great', 'awesome', 'amazing', 'fantastic'],
            formal: ['outstanding', 'exceptional', 'superior', 'remarkable'],
            context_aware: {
                'performance': ['outstanding', 'exceptional', 'superior'],
                'casual': ['great', 'awesome', 'amazing']
            }
        },
        'comprehensive': {
            casual: ['complete', 'full', 'thorough', 'detailed'],
            formal: ['extensive', 'exhaustive', 'all-encompassing', 'wide-ranging'],
            context_aware: {
                'analysis': ['thorough', 'detailed', 'extensive'],
                'report': ['complete', 'comprehensive', 'detailed']
            }
        },
        'sophisticated': {
            casual: ['advanced', 'complex', 'smart', 'clever'],
            formal: ['refined', 'elaborate', 'intricate', 'nuanced'],
            context_aware: {
                'technology': ['advanced', 'complex', 'smart'],
                'design': ['refined', 'elegant', 'polished']
            }
        },
        'utilize': {
            casual: ['use', 'apply', 'employ', 'work with'],
            formal: ['implement', 'deploy', 'leverage', 'harness'],
            context_aware: {
                'technology': ['use', 'apply', 'implement'],
                'business': ['leverage', 'employ', 'deploy']
            }
        },
        'demonstrate': {
            casual: ['show', 'prove', 'display', 'reveal'],
            formal: ['illustrate', 'exhibit', 'manifest', 'exemplify'],
            context_aware: {
                'research': ['show', 'illustrate', 'reveal'],
                'presentation': ['display', 'exhibit', 'demonstrate']
            }
        },
        'furthermore': {
            casual: ['also', 'plus', 'and', 'too'],
            formal: ['additionally', 'moreover', 'in addition', 'besides'],
            context_aware: {
                'academic': ['additionally', 'moreover'],
                'casual': ['also', 'plus', 'and']
            }
        },
        'consequently': {
            casual: ['so', 'thus', 'then', 'as a result'],
            formal: ['therefore', 'hence', 'accordingly', 'subsequently'],
            context_aware: {
                'logic': ['therefore', 'thus', 'hence'],
                'casual': ['so', 'then', 'as a result']
            }
        },
        'exceptional': {
            casual: ['amazing', 'outstanding', 'great', 'fantastic'],
            formal: ['remarkable', 'extraordinary', 'superior', 'distinguished'],
            context_aware: {
                'performance': ['outstanding', 'superior', 'excellent'],
                'quality': ['remarkable', 'extraordinary', 'exceptional']
            }
        }
    };

    const word_lower = word.toLowerCase();

    // Check if word exists in enhanced database
    if (synonymDatabase[word_lower]) {
        const synonymEntry = synonymDatabase[word_lower];

        // Contextual selection logic
        if (context && synonymEntry.context_aware) {
            for (const [contextType, synonyms] of Object.entries(synonymEntry.context_aware)) {
                if (context.toLowerCase().includes(contextType)) {
                    return synonyms[Math.floor(Math.random() * synonyms.length)];
                }
            }
        }

        // Default to casual synonyms for better humanization
        const synonyms = synonymEntry.casual || synonymEntry;
        return Array.isArray(synonyms) ?
               synonyms[Math.floor(Math.random() * synonyms.length)] :
               synonyms[Math.floor(Math.random() * synonyms.length)];
    }

    // Fallback to basic synonym database
    return getBasicSynonym(word_lower, posTag);
}

/**
 * Basic synonym database for fallback (matching original implementation)
 */
const SYNONYM_DATABASE = {
    // Adjectives (JJ)
    'JJ': {
        'good': ['excellent', 'great', 'fine', 'wonderful', 'superb', 'outstanding', 'remarkable', 'fantastic', 'terrific', 'awesome'],
        'bad': ['terrible', 'awful', 'horrible', 'dreadful', 'poor', 'lousy', 'rotten', 'nasty', 'unpleasant', 'disappointing'],
        'big': ['large', 'huge', 'enormous', 'massive', 'gigantic', 'vast', 'immense', 'colossal', 'tremendous', 'substantial'],
        'small': ['tiny', 'little', 'minute', 'compact', 'petite', 'miniature', 'microscopic', 'diminutive', 'modest', 'limited'],
        'important': ['crucial', 'vital', 'essential', 'significant', 'critical', 'key', 'major', 'fundamental', 'primary', 'central'],
        'different': ['distinct', 'unique', 'separate', 'various', 'diverse', 'alternative', 'contrasting', 'dissimilar', 'varied', 'other'],
        'new': ['fresh', 'recent', 'modern', 'latest', 'current', 'novel', 'innovative', 'contemporary', 'updated', 'brand-new'],
        'old': ['ancient', 'aged', 'elderly', 'vintage', 'antique', 'mature', 'seasoned', 'traditional', 'classic', 'outdated'],
        'high': ['elevated', 'tall', 'lofty', 'towering', 'superior', 'advanced', 'intense', 'extreme', 'peak', 'maximum'],
        'low': ['reduced', 'minimal', 'decreased', 'inferior', 'bottom', 'shallow', 'minor', 'slight', 'modest', 'limited'],
        'easy': ['simple', 'effortless', 'straightforward', 'uncomplicated', 'basic', 'elementary', 'smooth', 'manageable', 'clear', 'plain'],
        'hard': ['difficult', 'challenging', 'tough', 'complex', 'complicated', 'demanding', 'strenuous', 'arduous', 'rigorous', 'intense'],
        'fast': ['quick', 'rapid', 'swift', 'speedy', 'hasty', 'brisk', 'prompt', 'immediate', 'instant', 'accelerated'],
        'slow': ['gradual', 'leisurely', 'sluggish', 'delayed', 'unhurried', 'steady', 'measured', 'deliberate', 'prolonged', 'extended'],
        'significant': ['major', 'important', 'substantial', 'considerable', 'notable', 'meaningful', 'relevant', 'crucial', 'vital', 'key'],
        'comprehensive': ['complete', 'thorough', 'extensive', 'detailed', 'full', 'total', 'exhaustive', 'all-inclusive', 'wide-ranging', 'broad'],
        'effective': ['successful', 'efficient', 'productive', 'useful', 'powerful', 'potent', 'capable', 'competent', 'skillful', 'proficient'],
        'advanced': ['sophisticated', 'complex', 'developed', 'progressive', 'cutting-edge', 'state-of-the-art', 'modern', 'innovative', 'high-tech', 'evolved']
    },

    // Adverbs (RB)
    'RB': {
        'very': ['extremely', 'incredibly', 'remarkably', 'exceptionally', 'particularly', 'especially', 'quite', 'rather', 'pretty', 'fairly'],
        'really': ['truly', 'genuinely', 'actually', 'honestly', 'seriously', 'definitely', 'certainly', 'absolutely', 'completely', 'totally'],
        'quickly': ['rapidly', 'swiftly', 'speedily', 'hastily', 'promptly', 'immediately', 'instantly', 'briskly', 'efficiently', 'urgently'],
        'slowly': ['gradually', 'leisurely', 'steadily', 'carefully', 'deliberately', 'methodically', 'patiently', 'gently', 'cautiously', 'unhurriedly'],
        'often': ['frequently', 'regularly', 'commonly', 'typically', 'usually', 'generally', 'repeatedly', 'consistently', 'habitually', 'routinely'],
        'sometimes': ['occasionally', 'periodically', 'intermittently', 'sporadically', 'now and then', 'from time to time', 'once in a while', 'at times', 'every so often', 'infrequently'],
        'always': ['constantly', 'continuously', 'perpetually', 'consistently', 'invariably', 'forever', 'eternally', 'endlessly', 'permanently', 'unfailingly'],
        'never': ['not ever', 'at no time', 'under no circumstances', 'not once', 'not at all', 'by no means', 'absolutely not', 'certainly not', 'definitely not', 'positively not'],
        'clearly': ['obviously', 'evidently', 'apparently', 'plainly', 'distinctly', 'unmistakably', 'undoubtedly', 'certainly', 'definitely', 'undeniably'],
        'probably': ['likely', 'presumably', 'possibly', 'conceivably', 'potentially', 'maybe', 'perhaps', 'chances are', 'in all likelihood', 'most likely'],
        'furthermore': ['and', 'plus', 'also', 'on top of that', 'what\'s more', 'besides', 'additionally', 'moreover', 'in addition', 'as well'],
        'moreover': ['and', 'plus', 'also', 'what\'s more', 'besides', 'furthermore', 'additionally', 'in addition', 'as well', 'too'],
        'additionally': ['and', 'plus', 'also', 'on top of that', 'too', 'as well', 'furthermore', 'moreover', 'besides', 'in addition'],
        'consequently': ['so', 'which means', 'that\'s why', 'as a result', 'therefore', 'thus', 'hence', 'accordingly', 'for this reason', 'because of this'],
        'therefore': ['so', 'which means', 'that\'s why', 'hence', 'thus', 'consequently', 'as a result', 'for this reason', 'accordingly', 'because of this'],
        'thus': ['so', 'which means', 'that way', 'like this', 'in this way', 'therefore', 'hence', 'consequently', 'as a result', 'accordingly'],
        'hence': ['so', 'which is why', 'that\'s how', 'that\'s why', 'thus', 'therefore', 'consequently', 'as a result', 'for this reason', 'accordingly'],
        'subsequently': ['then', 'after that', 'next', 'later', 'afterwards', 'following that', 'in the aftermath', 'eventually', 'ultimately', 'finally'],
        'nevertheless': ['but', 'still', 'even so', 'however', 'yet', 'nonetheless', 'regardless', 'despite that', 'all the same', 'in spite of that'],
        'however': ['but', 'though', 'still', 'yet', 'even so', 'nevertheless', 'nonetheless', 'on the other hand', 'conversely', 'in contrast']
    },

    // Verbs (VB)
    'VB': {
        'show': ['demonstrate', 'display', 'exhibit', 'reveal', 'present', 'indicate', 'illustrate', 'manifest', 'expose', 'prove'],
        'make': ['create', 'produce', 'generate', 'construct', 'build', 'manufacture', 'form', 'develop', 'establish', 'cause'],
        'get': ['obtain', 'acquire', 'receive', 'gain', 'secure', 'achieve', 'attain', 'procure', 'fetch', 'retrieve'],
        'give': ['provide', 'offer', 'supply', 'deliver', 'present', 'grant', 'bestow', 'contribute', 'donate', 'hand over'],
        'use': ['utilize', 'employ', 'apply', 'implement', 'operate', 'handle', 'manipulate', 'exercise', 'practice', 'exploit'],
        'find': ['discover', 'locate', 'identify', 'detect', 'uncover', 'spot', 'encounter', 'come across', 'stumble upon', 'determine'],
        'think': ['believe', 'consider', 'suppose', 'assume', 'imagine', 'reckon', 'feel', 'suspect', 'presume', 'contemplate'],
        'know': ['understand', 'realize', 'recognize', 'comprehend', 'grasp', 'perceive', 'acknowledge', 'be aware of', 'be familiar with', 'appreciate'],
        'help': ['assist', 'aid', 'support', 'facilitate', 'contribute', 'serve', 'benefit', 'back', 'cooperate', 'collaborate'],
        'work': ['function', 'operate', 'perform', 'labor', 'toil', 'strive', 'endeavor', 'effort', 'serve', 'act'],
        'utilize': ['use', 'employ', 'apply', 'implement', 'operate', 'handle', 'manipulate', 'exercise', 'practice', 'exploit'],
        'implement': ['execute', 'carry out', 'put into practice', 'apply', 'perform', 'conduct', 'realize', 'accomplish', 'fulfill', 'enact'],
        'facilitate': ['help', 'assist', 'aid', 'support', 'enable', 'promote', 'encourage', 'foster', 'advance', 'expedite'],
        'demonstrate': ['show', 'display', 'exhibit', 'reveal', 'present', 'indicate', 'illustrate', 'manifest', 'prove', 'establish'],
        'establish': ['set up', 'create', 'found', 'institute', 'form', 'build', 'construct', 'develop', 'organize', 'launch'],
        'maintain': ['keep', 'preserve', 'sustain', 'continue', 'uphold', 'retain', 'support', 'conserve', 'protect', 'defend'],
        'generate': ['create', 'produce', 'make', 'cause', 'bring about', 'give rise to', 'spawn', 'engender', 'originate', 'develop'],
        'analyze': ['examine', 'study', 'investigate', 'scrutinize', 'evaluate', 'assess', 'review', 'inspect', 'dissect', 'break down'],
        'evaluate': ['assess', 'judge', 'appraise', 'review', 'examine', 'analyze', 'estimate', 'rate', 'measure', 'weigh'],
        'optimize': ['improve', 'enhance', 'refine', 'perfect', 'fine-tune', 'streamline', 'maximize', 'upgrade', 'boost', 'polish']
    },

    // Nouns (NN)
    'NN': {
        'thing': ['item', 'object', 'element', 'matter', 'subject', 'issue', 'aspect', 'factor', 'component', 'entity'],
        'way': ['method', 'approach', 'manner', 'technique', 'strategy', 'procedure', 'process', 'system', 'means', 'route'],
        'time': ['period', 'moment', 'instance', 'occasion', 'duration', 'interval', 'phase', 'stage', 'era', 'epoch'],
        'person': ['individual', 'human', 'being', 'character', 'figure', 'someone', 'citizen', 'resident', 'inhabitant', 'soul'],
        'place': ['location', 'spot', 'site', 'position', 'area', 'region', 'zone', 'venue', 'destination', 'setting'],
        'problem': ['issue', 'difficulty', 'challenge', 'obstacle', 'complication', 'dilemma', 'trouble', 'concern', 'matter', 'situation'],
        'idea': ['concept', 'notion', 'thought', 'suggestion', 'proposal', 'plan', 'scheme', 'theory', 'hypothesis', 'perspective'],
        'system': ['structure', 'framework', 'organization', 'arrangement', 'setup', 'network', 'mechanism', 'process', 'method', 'approach'],
        'information': ['data', 'details', 'facts', 'knowledge', 'intelligence', 'material', 'content', 'evidence', 'documentation', 'statistics'],
        'example': ['instance', 'case', 'illustration', 'sample', 'specimen', 'model', 'demonstration', 'representation', 'prototype', 'pattern'],
        'methodology': ['method', 'approach', 'technique', 'procedure', 'system', 'strategy', 'process', 'framework', 'protocol', 'way'],
        'implementation': ['execution', 'application', 'realization', 'deployment', 'installation', 'establishment', 'introduction', 'adoption', 'enactment', 'fulfillment'],
        'optimization': ['improvement', 'enhancement', 'refinement', 'perfection', 'upgrading', 'fine-tuning', 'streamlining', 'maximization', 'betterment', 'advancement'],
        'analysis': ['examination', 'study', 'investigation', 'review', 'assessment', 'evaluation', 'scrutiny', 'inspection', 'research', 'exploration'],
        'evaluation': ['assessment', 'appraisal', 'review', 'judgment', 'analysis', 'examination', 'estimation', 'rating', 'measurement', 'critique'],
        'framework': ['structure', 'system', 'foundation', 'platform', 'basis', 'infrastructure', 'architecture', 'skeleton', 'outline', 'scheme'],
        'paradigm': ['model', 'framework', 'pattern', 'template', 'example', 'standard', 'archetype', 'prototype', 'concept', 'approach'],
        'infrastructure': ['foundation', 'base', 'structure', 'framework', 'system', 'network', 'backbone', 'support', 'platform', 'architecture']
    }
};

/**
 * Determine POS tag for a word using pattern matching (matching falconService.js)
 */
function determinePOSTag(word) {
    // Adjective patterns (JJ)
    if (word.match(/^(very|quite|rather|extremely|highly|incredibly|amazingly|particularly|especially|remarkably)$/)) return 'RB';
    if (word.match(/(ful|less|ous|ive|able|ible|al|ic|ed|ing)$/)) return 'JJ';
    if (word.match(/^(good|bad|great|small|large|big|new|old|high|low|long|short|important|different|possible|available|necessary|special|certain|clear|simple|easy|difficult|hard|strong|weak|beautiful|ugly|happy|sad|young|early|late|recent|current|future|past|present|real|true|false|right|wrong|correct|proper|main|major|minor|basic|general|specific|particular|common|rare|unique|similar|different|various|several|many|few|much|little|more|most|less|least|best|worst|better|worse|first|last|next|previous|final|initial|original|natural|artificial|public|private|personal|professional|social|political|economic|financial|legal|medical|technical|scientific|educational|cultural|historical|modern|traditional|contemporary|ancient|recent|future|international|national|local|regional|global|worldwide|domestic|foreign|external|internal|upper|lower|middle|central|northern|southern|eastern|western|left|right|front|back|top|bottom|inside|outside|above|below|near|far|close|distant|direct|indirect|positive|negative|active|passive|open|closed|free|busy|full|empty|complete|incomplete|perfect|imperfect|normal|abnormal|regular|irregular|standard|special|ordinary|extraordinary|typical|unusual|common|rare|popular|unpopular|famous|unknown|successful|unsuccessful|effective|ineffective|efficient|inefficient|useful|useless|helpful|harmful|safe|dangerous|secure|insecure|stable|unstable|reliable|unreliable|accurate|inaccurate|exact|approximate|precise|vague|clear|unclear|obvious|hidden|visible|invisible|bright|dark|light|heavy|soft|hard|smooth|rough|hot|cold|warm|cool|wet|dry|clean|dirty|fresh|old|sharp|dull|loud|quiet|fast|slow|quick|gradual|sudden|immediate|delayed|temporary|permanent|brief|long|short|tall|wide|narrow|thick|thin|deep|shallow|rich|poor|expensive|cheap|valuable|worthless)$/)) return 'JJ';

    // Adverb patterns (RB)
    if (word.match(/(ly|ward|wise)$/)) return 'RB';
    if (word.match(/^(very|quite|rather|really|actually|basically|generally|usually|normally|typically|commonly|frequently|often|sometimes|occasionally|rarely|seldom|never|always|constantly|continuously|regularly|irregularly|immediately|instantly|quickly|slowly|gradually|suddenly|recently|currently|previously|formerly|originally|initially|finally|eventually|ultimately|definitely|certainly|probably|possibly|maybe|perhaps|obviously|clearly|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|absolutely|completely|totally|entirely|fully|partly|partially|mostly|mainly|primarily|chiefly|largely|generally|specifically|particularly|especially|notably|remarkably|significantly|considerably|substantially|slightly|somewhat|fairly|pretty|quite|rather|extremely|highly|incredibly|amazingly|surprisingly|interestingly|fortunately|unfortunately|hopefully|thankfully|regrettably|sadly|happily|luckily|unluckily|naturally|obviously|clearly|evidently|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|certainly|definitely|probably|possibly|maybe|perhaps|here|there|everywhere|anywhere|somewhere|nowhere|inside|outside|above|below|beneath|under|over|through|across|around|along|beside|behind|before|after|during|within|without|beyond|towards|away|forward|backward|upward|downward|inward|outward|homeward|eastward|westward|northward|southward|today|tomorrow|yesterday|now|then|soon|later|earlier|before|after|already|still|yet|again|once|twice|often|always|never|sometimes|usually|normally|typically|generally|specifically|particularly|especially|mainly|mostly|largely|primarily|chiefly|basically|essentially|fundamentally|originally|initially|finally|eventually|ultimately|immediately|instantly|quickly|slowly|gradually|suddenly|recently|currently|previously|formerly|definitely|certainly|probably|possibly|maybe|perhaps|obviously|clearly|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|absolutely|completely|totally|entirely|fully|partly|partially|slightly|somewhat|fairly|pretty|quite|rather|extremely|highly|incredibly|amazingly|surprisingly|interestingly|fortunately|unfortunately|hopefully|thankfully|regrettably|sadly|happily|luckily|unluckily|naturally)$/)) return 'RB';

    // Verb patterns (VB)
    if (word.match(/(ed|ing|s)$/)) return 'VB';
    if (word.match(/^(is|are|was|were|be|been|being|have|has|had|having|do|does|did|doing|will|would|could|should|might|may|can|must|shall|ought|need|dare|used)$/)) return 'VB';

    // Noun patterns (NN) - default
    return 'NN';
}

/**
 * Simple POS tagging for JavaScript (inspired by NLTK approach)
 */
function getPOSTags(sentence) {
    const words = sentence.match(/\w+/g) || [];
    const tags = [];

    for (const word of words) {
        const pos = determinePOSTag(word.toLowerCase());
        tags.push({ word, pos });
    }

    return tags;
}

/**
 * Get synonyms based on POS tag (inspired by NLTK WordNet approach)
 */
function getSynonymsByPOS(word, posTag) {
    // Get POS category
    let posCategory = 'NN'; // Default to noun
    if (posTag.startsWith('JJ')) posCategory = 'JJ';
    else if (posTag.startsWith('RB')) posCategory = 'RB';
    else if (posTag.startsWith('VB')) posCategory = 'VB';

    // Look up synonyms
    const categoryDatabase = SYNONYM_DATABASE[posCategory] || {};
    return categoryDatabase[word] || [];
}

/**
 * Select best synonym based on frequency and context
 */
function selectBestSynonym(synonyms, originalWord) {
    if (synonyms.length === 0) return originalWord;

    // For now, select randomly from first 3 synonyms (most common)
    const topSynonyms = synonyms.slice(0, Math.min(3, synonyms.length));
    return topSynonyms[Math.floor(Math.random() * topSynonyms.length)];
}

/**
 * Preserve original capitalization pattern
 */
function preserveCapitalization(original, replacement) {
    if (!original || !replacement) return replacement;

    // All uppercase
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }

    // First letter uppercase
    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }

    // All lowercase
    return replacement.toLowerCase();
}

/**
 * Replace word based on POS tag (inspired by NLTK approach)
 */
function replaceWordWithPOS(word, posTag, aggressiveness) {
    // Skip very short words or common words
    if (word.length <= 2 || isCommonWord(word)) {
        return word;
    }

    // Determine replacement probability based on POS tag
    let replacementProbability = 0.3; // Base probability

    if (posTag.startsWith('JJ')) {
        // Adjectives - higher replacement rate
        replacementProbability = 0.6 * aggressiveness;
    } else if (posTag.startsWith('RB')) {
        // Adverbs - higher replacement rate
        replacementProbability = 0.5 * aggressiveness;
    } else if (posTag.startsWith('VB')) {
        // Verbs - moderate replacement rate
        replacementProbability = 0.4 * aggressiveness;
    } else {
        // Nouns and others - lower replacement rate
        replacementProbability = 0.2 * aggressiveness;
    }

    // Random decision to replace
    if (Math.random() > replacementProbability) {
        return word;
    }

    // Get synonyms based on POS tag
    const synonyms = getSynonymsByPOS(word.toLowerCase(), posTag);

    if (synonyms.length === 0) {
        return word;
    }

    // Choose most appropriate synonym (frequency-based selection)
    const chosenSynonym = selectBestSynonym(synonyms, word);

    // Preserve original capitalization
    return preserveCapitalization(word, chosenSynonym);
}

/**
 * Check if word is too common to replace
 */
function isCommonWord(word) {
    const commonWords = new Set([
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
        'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
        'between', 'among', 'under', 'over', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
        'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
        'might', 'must', 'can', 'shall', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
        'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
        'her', 'its', 'our', 'their'
    ]);

    return commonWords.has(word.toLowerCase());
}

/**
 * Apply contractions to make text more casual
 */
function applyContractions(text) {
    const contractions = {
        'do not': 'don\'t',
        'will not': 'won\'t',
        'cannot': 'can\'t',
        'should not': 'shouldn\'t',
        'would not': 'wouldn\'t',
        'could not': 'couldn\'t',
        'it is': 'it\'s',
        'that is': 'that\'s',
        'there is': 'there\'s',
        'we are': 'we\'re',
        'they are': 'they\'re',
        'you are': 'you\'re',
        'I am': 'I\'m',
        'he is': 'he\'s',
        'she is': 'she\'s',
        'we have': 'we\'ve',
        'they have': 'they\'ve',
        'I have': 'I\'ve',
        'you have': 'you\'ve'
    };
    
    let result = text;
    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, contracted);
    });
    
    return result;
}

/**
 * Add casual qualifiers to make text more human-like
 */
function addCasualQualifiers(text, frequency = 0.15) {
    const qualifiers = [
        'pretty much', 'basically', 'sort of', 'kind of', 'more or less',
        'I think', 'probably', 'maybe', 'I guess', 'seems like'
    ];
    
    return text.replace(/\b(is|are|will|can|should|would|could)\b/g, (match) => {
        if (Math.random() < frequency) {
            const qualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)];
            return `${qualifier} ${match}`;
        }
        return match;
    });
}

/**
 * Clean symbols and spacing (similar to Python clean_symbols function)
 */
function cleanSymbols(text) {
    // Remove extra spaces
    let cleaned = text.replace(/\s+/g, ' ');

    // Fix spacing around punctuation - be more careful to preserve word spacing
    cleaned = cleaned.replace(/\s+([.!?,:;])/g, '$1'); // Remove space before punctuation
    cleaned = cleaned.replace(/([.!?])\s*([A-Z])/g, '$1 $2'); // Ensure space after sentence endings
    cleaned = cleaned.replace(/([.!?])([A-Z])/g, '$1 $2'); // Fix missing space after sentence endings
    cleaned = cleaned.replace(/,([A-Za-z])/g, ', $1'); // Fix missing space after commas

    return cleaned.trim();
}

/**
 * Main NLTK-inspired processing function (matching falconService.js)
 */
function processTextWithNLTKApproach(text, options = {}) {
    const { aggressiveness = 0.7, maintainTone = true, useAdvancedSynonyms = true } = options;

    // Preserve newlines with placeholder (similar to Python approach)
    const newlinePlaceholder = "庄周"; // Using same placeholder as Python code
    const textWithPlaceholders = text.replace(/\n/g, newlinePlaceholder);

    // Split into sentences while preserving symbols - improved regex
    const sentences = textWithPlaceholders.match(/[^.!?]*[.!?]+\s*|[^.!?]+$/g) || [textWithPlaceholders];

    const humanizedSentences = [];

    for (const sentence of sentences) {
        if (!sentence.trim()) {
            humanizedSentences.push(sentence);
            continue;
        }

        // Tokenize sentence into words and symbols
        const tokens = sentence.match(/\w+|[^\w\s]+/g) || [];

        // Get POS tags for the sentence
        const posTags = getPOSTags(sentence);

        // Process each token with POS-aware synonym replacement
        const humanizedTokens = [];
        let wordIndex = 0;

        for (const token of tokens) {
            if (/^\w+$/.test(token)) {
                // It's a word - apply POS-aware replacement
                const posTag = posTags[wordIndex] || { word: token, pos: 'NN' };
                const humanizedWord = replaceWordWithPOS(token, posTag.pos, aggressiveness);
                humanizedTokens.push(humanizedWord);
                wordIndex++;
            } else {
                // It's punctuation or symbol - keep as is
                humanizedTokens.push(token);
            }
        }

        // Reconstruct sentence with proper spacing
        let humanizedSentence = '';
        for (let i = 0; i < humanizedTokens.length; i++) {
            const token = humanizedTokens[i];

            // Add space before word tokens (except first token)
            if (i > 0 && /^\w/.test(token) && /\w$/.test(humanizedTokens[i-1])) {
                humanizedSentence += ' ';
            }

            humanizedSentence += token;
        }

        // Clean up spacing around symbols
        humanizedSentence = cleanSymbols(humanizedSentence);

        humanizedSentences.push(humanizedSentence);
    }

    // Join sentences and restore newlines
    let result = humanizedSentences.join('');

    // Final cleanup of spacing issues
    result = cleanSymbols(result);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Basic humanization with simple transformations
 */
function applyBasicHumanization(text, options = {}) {
    const { aggressiveness = 0.7 } = options;
    
    let result = text;
    
    // Apply contractions
    result = applyContractions(result);
    
    // Add casual qualifiers
    if (aggressiveness > 0.5) {
        result = addCasualQualifiers(result, aggressiveness * 0.15);
    }
    
    // Simple synonym replacement for common AI triggers
    const simpleReplacements = {
        'furthermore': 'and',
        'moreover': 'plus',
        'additionally': 'also',
        'consequently': 'so',
        'therefore': 'so',
        'utilize': 'use',
        'implement': 'do',
        'facilitate': 'help'
    };
    
    Object.entries(simpleReplacements).forEach(([formal, casual]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, casual);
    });
    
    return result;
}

/**
 * LRU Cache implementation for memory-efficient caching
 */
class LRUCache {
    constructor(maxSize = 1000) {
        this.maxSize = maxSize;
        this.cache = new Map();
    }

    get(key) {
        if (this.cache.has(key)) {
            // Move to end (most recently used)
            const value = this.cache.get(key);
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }
        return null;
    }

    set(key, value) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        } else if (this.cache.size >= this.maxSize) {
            // Remove least recently used (first item)
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    clear() {
        this.cache.clear();
    }

    size() {
        return this.cache.size;
    }
}

// Optimized caching system with different cache sizes per tier
const optimizedCaches = {
    synonym: new LRUCache(2000), // Larger cache for synonyms
    pos: new LRUCache(1000),     // Medium cache for POS tags
    word: new LRUCache(500),     // Smaller cache for word analysis
    sentence: new LRUCache(100)  // Small cache for sentence patterns
};

// Optimized regex patterns for better performance
const optimizedRegexPatterns = {
    sentences: /[^.!?]*[.!?]+\s*|[^.!?]+$/g,
    tokens: /\w+|[^\w\s]+/g,
    words: /\w+/g,
    wordToken: /^\w+$/
};

// Pre-computed sets for O(1) lookup (performance optimization)
const commonWordsSet = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall', 'ought'
]);

const commonAdjectivesSet = new Set([
    'good', 'bad', 'big', 'small', 'important', 'significant', 'excellent', 'great', 'fine',
    'wonderful', 'terrible', 'awful', 'large', 'huge', 'tiny', 'little', 'crucial', 'vital'
]);

/**
 * Performance tiers configuration (matching falconService.js)
 */
const PERFORMANCE_TIERS = {
    free: {
        processingDelay: 500, // Reduced delay for better UX
        batchSize: 20,
        cacheEnabled: false,
        parallelProcessing: false,
        maxCacheSize: 0,
        useOptimizedRegex: false,
        usePrecomputedSets: false,
        algorithmComplexity: 'basic'
    },
    premium: {
        processingDelay: 0,
        batchSize: 100,
        cacheEnabled: true,
        parallelProcessing: true,
        maxCacheSize: 1000,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        useLRUCache: true,
        algorithmComplexity: 'optimized'
    },
    admin: {
        processingDelay: 0,
        batchSize: 200,
        cacheEnabled: true,
        parallelProcessing: true,
        optimizedAlgorithms: true,
        maxCacheSize: 2000,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        useLRUCache: true,
        useAdvancedOptimizations: true,
        algorithmComplexity: 'maximum'
    }
};

/**
 * Detect user tier from session or request context
 */
function getUserTier(options = {}) {
    // Check for explicit tier in options (for testing)
    if (options.userTier) {
        return options.userTier;
    }

    // Check for user session data
    if (options.user) {
        const subscriptionTier = options.user.subscriptionTier || 'free';
        const userRole = options.user.role || 'user';

        // Map subscription tiers to performance tiers
        if (userRole === 'admin' || userRole === 'owner') return 'admin';
        if (subscriptionTier.includes('premium') || subscriptionTier.includes('pro')) return 'premium';
    }

    // Default to free tier
    return 'free';
}

/**
 * Enhanced NLTK-inspired processing with tier-specific optimizations
 */
async function processTextWithNLTKApproachOptimized(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        useAdvancedSynonyms = true,
        performanceConfig = PERFORMANCE_TIERS.free,
        userTier = 'free'
    } = options;

    // Apply processing delay for free users (feature limitation)
    if (performanceConfig.processingDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, performanceConfig.processingDelay));
    }

    // Use optimized regex patterns for premium/admin users
    const sentenceRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.sentences : /[^.!?]*[.!?]+\s*|[^.!?]+$/g;
    const tokenRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.tokens : /\w+|[^\w\s]+/g;

    // Preserve newlines with placeholder
    const newlinePlaceholder = "庄周";
    const textWithPlaceholders = text.replace(/\n/g, newlinePlaceholder);

    // Split into sentences using optimized regex
    const sentences = textWithPlaceholders.match(sentenceRegex) || [textWithPlaceholders];

    // Use parallel processing for premium/admin users with larger texts
    if (performanceConfig.parallelProcessing && sentences.length > 3) {
        return await processTextParallel(sentences, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex,
            newlinePlaceholder
        });
    } else {
        return await processTextSequential(sentences, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex,
            newlinePlaceholder
        });
    }
}

/**
 * Parallel processing for premium/admin users - processes sentences concurrently
 */
async function processTextParallel(sentences, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex, newlinePlaceholder } = options;

    // Process sentences in parallel batches
    const batchSize = performanceConfig.batchSize;
    const batches = [];

    for (let i = 0; i < sentences.length; i += batchSize) {
        batches.push(sentences.slice(i, i + batchSize));
    }

    const processedBatches = await Promise.all(batches.map(async (batch) => {
        return await Promise.all(batch.map(async (sentence) => {
            return await processSingleSentenceOptimized(sentence, {
                aggressiveness,
                useAdvancedSynonyms,
                performanceConfig,
                userTier,
                tokenRegex
            });
        }));
    }));

    // Flatten results and cleanup
    const humanizedSentences = processedBatches.flat();
    let result = humanizedSentences.join('');
    result = cleanSymbolsOptimized(result, performanceConfig);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Sequential processing for free users - processes sentences one by one
 */
async function processTextSequential(sentences, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex, newlinePlaceholder } = options;

    const humanizedSentences = [];

    for (const sentence of sentences) {
        const processedSentence = await processSingleSentenceOptimized(sentence, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex
        });
        humanizedSentences.push(processedSentence);
    }

    // Join results and cleanup
    let result = humanizedSentences.join('');
    result = cleanSymbolsOptimized(result, performanceConfig);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Optimized single sentence processing with caching and performance enhancements
 */
async function processSingleSentenceOptimized(sentence, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex } = options;

    if (!sentence.trim()) {
        return sentence;
    }

    // Check sentence cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cacheKey = `sentence_${sentence}_${aggressiveness}_${userTier}`;
        const cached = optimizedCaches.sentence.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    // Tokenize sentence using optimized regex
    const tokens = sentence.match(tokenRegex) || [];

    // Get POS tags with caching
    const posTags = getPOSTagsOptimized(sentence, performanceConfig);

    // Process tokens with optimized replacement
    const humanizedTokens = [];
    let wordIndex = 0;

    for (const token of tokens) {
        if (optimizedRegexPatterns.wordToken.test(token)) {
            const posTag = posTags[wordIndex] || { word: token, pos: 'NN' };
            const humanizedWord = await replaceWordWithPOSOptimized(
                token,
                posTag.pos,
                aggressiveness,
                useAdvancedSynonyms,
                performanceConfig
            );
            humanizedTokens.push(humanizedWord);
            wordIndex++;
        } else {
            humanizedTokens.push(token);
        }
    }

    // Reconstruct sentence with optimized spacing
    let humanizedSentence = reconstructSentenceOptimized(humanizedTokens, performanceConfig);

    // Cache result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cacheKey = `sentence_${sentence}_${aggressiveness}_${userTier}`;
        optimizedCaches.sentence.set(cacheKey, humanizedSentence);
    }

    return humanizedSentence;
}

/**
 * Main humanization function with tier support (matching falconService.js)
 */
async function humanizeWithNLTKApproach(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        useAdvancedSynonyms = true,
        user = null // User session for tier detection
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    const userTier = getUserTier({ user, ...options });
    const performanceConfig = PERFORMANCE_TIERS[userTier];

    try {
        console.log(`🔬 Starting NLTK-inspired humanization (${userTier} tier)...`);

        // Apply NLTK-inspired processing with tier-specific optimizations
        const humanizedText = await processTextWithNLTKApproachOptimized(text, {
            aggressiveness,
            maintainTone,
            useAdvancedSynonyms,
            performanceConfig,
            userTier
        });

        const processingTime = Date.now() - startTime;

        return {
            success: true,
            text: humanizedText,
            originalText: text,
            method: 'nltk-inspired',
            processingTime,
            originalLength: text.length,
            newLength: humanizedText.length,
            transformationRate: ((text.length - humanizedText.length) / text.length * 100).toFixed(2),
            userTier,
            performanceConfig: {
                tier: userTier,
                processingDelay: performanceConfig.processingDelay,
                cacheEnabled: performanceConfig.cacheEnabled,
                parallelProcessing: performanceConfig.parallelProcessing
            }
        };
    } catch (error) {
        console.error('NLTK humanization error:', error);
        return {
            success: false,
            error: error.message || 'Failed to process text with NLTK approach',
            originalText: text,
            processingTime: Date.now() - startTime
        };
    }
}

/**
 * Optimized POS tagging with caching and performance enhancements
 */
function getPOSTagsOptimized(sentence, performanceConfig) {
    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.pos.get(sentence);
        if (cached) {
            return cached;
        }
    }

    // Use optimized regex for premium/admin users
    const wordRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.words : /\w+/g;

    const words = sentence.match(wordRegex) || [];
    const tags = [];

    for (const word of words) {
        const pos = determinePOSTagOptimized(word.toLowerCase(), performanceConfig);
        tags.push({ word, pos });
    }

    // Cache result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.pos.set(sentence, tags);
    }

    return tags;
}

/**
 * Optimized POS tag determination with performance enhancements
 */
function determinePOSTagOptimized(word, performanceConfig) {
    // Use precomputed sets for O(1) lookup for premium/admin users
    if (performanceConfig.usePrecomputedSets) {
        // Common adjectives (pre-computed set for O(1) lookup)
        if (commonAdjectivesSet.has(word)) return 'JJ';
    }

    // Fallback to basic pattern matching
    return determinePOSTag(word);
}

/**
 * Optimized word replacement with advanced caching and performance tiers
 */
async function replaceWordWithPOSOptimized(word, posTag, aggressiveness, useAdvancedSynonyms, performanceConfig) {
    // Skip very short words or common words using optimized lookup
    if (word.length <= 2 || isCommonWordOptimized(word, performanceConfig)) {
        return word;
    }

    const cacheKey = `${word.toLowerCase()}_${posTag}_${aggressiveness}`;

    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.word.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    // Determine replacement probability based on POS tag
    let replacementProbability = 0.3; // Base probability

    if (posTag.startsWith('JJ')) {
        // Adjectives - higher replacement rate
        replacementProbability = 0.6 * aggressiveness;
    } else if (posTag.startsWith('RB')) {
        // Adverbs - higher replacement rate
        replacementProbability = 0.5 * aggressiveness;
    } else if (posTag.startsWith('VB')) {
        // Verbs - moderate replacement rate
        replacementProbability = 0.4 * aggressiveness;
    } else {
        // Nouns and others - lower replacement rate
        replacementProbability = 0.2 * aggressiveness;
    }

    // Random decision to replace
    if (Math.random() > replacementProbability) {
        if (performanceConfig.cacheEnabled) {
            optimizedCaches.word.set(cacheKey, word);
        }
        return word;
    }

    // Get synonyms with optimized lookup
    const synonyms = getSynonymsByPOSOptimized(word.toLowerCase(), posTag, performanceConfig);

    if (synonyms.length === 0) {
        if (performanceConfig.cacheEnabled) {
            optimizedCaches.word.set(cacheKey, word);
        }
        return word;
    }

    // Choose synonym with optimized selection
    const chosenSynonym = selectBestSynonymOptimized(synonyms, word, performanceConfig);
    const result = preserveCapitalization(word, chosenSynonym);

    // Cache the result
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.word.set(cacheKey, result);
    }

    return result;
}

/**
 * Optimized common word checking with pre-computed sets
 */
function isCommonWordOptimized(word, performanceConfig) {
    if (performanceConfig.usePrecomputedSets) {
        return commonWordsSet.has(word.toLowerCase());
    }
    return isCommonWord(word);
}

/**
 * Optimized synonym lookup with caching and performance enhancements
 */
function getSynonymsByPOSOptimized(word, posTag, performanceConfig) {
    const cacheKey = `${word}_${posTag}`;

    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.synonym.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    const synonyms = getSynonymsByPOS(word, posTag);

    // Cache the result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.synonym.set(cacheKey, synonyms);
    }

    return synonyms;
}

/**
 * Optimized synonym selection with performance-based algorithms
 */
function selectBestSynonymOptimized(synonyms, originalWord, performanceConfig) {
    if (synonyms.length === 0) return originalWord;

    // For admin users, use more sophisticated selection
    if (performanceConfig.algorithmComplexity === 'maximum') {
        // Select based on word length similarity and frequency
        const targetLength = originalWord.length;
        const scored = synonyms.map(syn => ({
            word: syn,
            score: Math.abs(syn.length - targetLength) + Math.random() * 0.5
        }));
        scored.sort((a, b) => a.score - b.score);
        return scored[0].word;
    }

    // For premium users, use frequency-based selection
    if (performanceConfig.algorithmComplexity === 'optimized') {
        return selectBestSynonym(synonyms, originalWord);
    }

    // For free users, use simple random selection
    return synonyms[Math.floor(Math.random() * synonyms.length)];
}

/**
 * Optimized sentence reconstruction with performance enhancements
 */
function reconstructSentenceOptimized(tokens, performanceConfig) {
    // For premium/admin users, use optimized reconstruction
    if (performanceConfig.algorithmComplexity !== 'basic') {
        return reconstructSentenceAdvanced(tokens);
    }

    // For free users, use basic reconstruction
    return tokens.join('');
}

/**
 * Advanced sentence reconstruction with proper spacing
 */
function reconstructSentenceAdvanced(tokens) {
    let result = '';
    for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        const nextToken = tokens[i + 1];

        result += token;

        // Add space if current token is a word and next token is also a word
        if (/^\w+$/.test(token) && nextToken && /^\w+$/.test(nextToken)) {
            result += ' ';
        }
    }
    return result;
}

/**
 * Optimized symbol cleaning with performance enhancements
 */
function cleanSymbolsOptimized(text, performanceConfig) {
    // For premium/admin users, use advanced cleaning
    if (performanceConfig.algorithmComplexity !== 'basic') {
        return cleanSymbolsAdvanced(text);
    }

    // For free users, use basic cleaning
    return cleanSymbols(text);
}

/**
 * Advanced symbol cleaning with better spacing
 */
function cleanSymbolsAdvanced(text) {
    return text
        .replace(/\s+/g, ' ')           // Multiple spaces to single space
        .replace(/\s+([.!?])/g, '$1')   // Remove space before punctuation
        .replace(/([.!?])\s*([A-Z])/g, '$1 $2') // Ensure space after sentence endings
        .replace(/,\s*/g, ', ')         // Ensure space after commas
        .trim();
}

/**
 * Netlify function handler
 */
export const handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({
                success: false,
                error: `Method ${event.httpMethod} Not Allowed`
            }),
        };
    }

    try {
        const { text, options = {} } = JSON.parse(event.body);

        if (!text || typeof text !== 'string') {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    error: 'Text is required and must be a string'
                })
            };
        }

        const startTime = Date.now();
        const result = await humanizeWithNLTKApproach(text, options);
        const totalProcessingTime = Date.now() - startTime;

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                ...result,
                totalProcessingTime,
                method: 'nltk-humanizer',
                timestamp: new Date().toISOString()
            })
        };

    } catch (error) {
        console.error('Netlify function error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                success: false,
                error: 'Internal server error',
                message: error.message,
                timestamp: new Date().toISOString()
            })
        };
    }
};

export {
    processTextWithNLTKApproach,
    applyBasicHumanization,
    humanizeWithNLTKApproach,
    getUserTier,
    PERFORMANCE_TIERS
};
